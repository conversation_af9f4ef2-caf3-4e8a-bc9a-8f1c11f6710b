<?php

namespace App\Http\Controllers;

use App\Models\Package;
use App\Helpers\ImageHelper;
use Illuminate\Http\Request;

class PackageController extends BaseResourceController
{
    protected function getModelClass()
    {
        return Package::class;
    }

    protected function getViewPrefix()
    {
        return 'packages';
    }

    protected function getRoutePrefix()
    {
        return 'packages';
    }

    protected function getImageDirectory()
    {
        return 'packages';
    }

    protected function getValidationRules(Request $request, $model = null)
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
        ];
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Package  $package
     * @return \Illuminate\Http\Response
     */
    public function show(Package $package)
    {
        // Handle AJAX request for admin
        $ajaxResponse = $this->handleAjaxShow($package, [
            'name', 'description', 'price', 'image_url'
        ]);

        if ($ajaxResponse) {
            return $ajaxResponse;
        }

        return view('packages.show', compact('package'));
    }

    // Store method for creating new package
    public function store(Request $request)
    {
        try {
            $data = $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'required|string',
                'price' => 'required|numeric|min:0',
                'image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
            ]);

            // Handle image upload
            if ($request->hasFile('image')) {
                $data['image'] = ImageHelper::uploadImage($request->file('image'), 'packages');
            }

            $package = Package::create($data);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'เพิ่มแพ็กเกจสำเร็จ',
                    'package' => $package
                ]);
            }

            return redirect()->route('admin.packages.index')->with('success', 'เพิ่มแพ็กเกจสำเร็จ');
        } catch (\Exception $e) {
            \Log::error('Error creating package: ' . $e->getMessage());

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'เกิดข้อผิดพลาดในการเพิ่มแพ็กเกจ: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'เกิดข้อผิดพลาดในการเพิ่มแพ็กเกจ');
        }
    }

    // Override update method to handle AJAX requests
    public function update(Request $request, Package $package)
    {
        try {
            $data = $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'required|string',
                'price' => 'required|numeric|min:0',
                'image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
            ]);

            // Handle image upload
            if ($request->hasFile('image')) {
                // Delete old image
                if ($package->image) {
                    ImageHelper::deleteImage($package->image);
                }
                $data['image'] = ImageHelper::uploadImage($request->file('image'), 'packages');
            }

            $package->update($data);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'อัปเดตแพ็กเกจสำเร็จ',
                    'package' => $package,
                    'image_url' => $package->image_url
                ]);
            }

            return redirect()->route('admin.packages.index')->with('success', 'อัปเดตแพ็กเกจสำเร็จ');
        } catch (\Exception $e) {
            \Log::error('Error updating package: ' . $e->getMessage());

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'เกิดข้อผิดพลาดในการอัปเดตแพ็กเกจ: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'เกิดข้อผิดพลาดในการอัปเดตแพ็กเกจ');
        }
    }

    // หลังบ้าน: ลบแพ็กเกจหลายรายการ
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:packages,id'
        ]);

        try {
            $packages = Package::whereIn('id', $request->ids)->get();

            // Delete images first
            foreach ($packages as $package) {
                if ($package->image) {
                    ImageHelper::deleteImage($package->image);
                }
            }

            // Delete packages
            Package::whereIn('id', $request->ids)->delete();

            return response()->json([
                'success' => true,
                'message' => 'ลบแพ็กเกจ ' . count($request->ids) . ' รายการสำเร็จ'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการลบข้อมูล'
            ], 500);
        }
    }
}
