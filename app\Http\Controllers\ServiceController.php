<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Helpers\ImageHelper;
use Illuminate\Http\Request;

class ServiceController extends BaseResourceController
{
    protected function getModelClass()
    {
        return Service::class;
    }

    protected function getViewPrefix()
    {
        return 'services';
    }

    protected function getRoutePrefix()
    {
        return 'services';
    }

    protected function getImageDirectory()
    {
        return 'services';
    }

    protected function getValidationRules(Request $request, $model = null)
    {
        return [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
        ];
    }

    // หน้าบ้าน: แสดงรายละเอียดบริการ
    public function show(Service $service)
    {
        // Handle AJAX request for admin
        $ajaxResponse = $this->handleAjaxShow($service, [
            'title', 'description', 'price', 'image_url'
        ]);

        if ($ajaxResponse) {
            return $ajaxResponse;
        }

        return view('services.show', compact('service'));
    }

    // Store method for creating new service
    public function store(Request $request)
    {
        try {
            $data = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'required|string',
                'price' => 'required|numeric|min:0',
                'image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
            ]);

            // Handle image upload
            if ($request->hasFile('image')) {
                $data['image'] = ImageHelper::uploadImage($request->file('image'), 'services');
            }

            $service = Service::create($data);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'เพิ่มบริการสำเร็จ',
                    'service' => $service
                ]);
            }

            return redirect()->route('admin.services.index')->with('success', 'เพิ่มบริการสำเร็จ');
        } catch (\Exception $e) {
            \Log::error('Error creating service: ' . $e->getMessage());

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'เกิดข้อผิดพลาดในการเพิ่มบริการ: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'เกิดข้อผิดพลาดในการเพิ่มบริการ');
        }
    }

    // Override update method to handle AJAX requests
    public function update(Request $request, Service $service)
    {
        try {
            $data = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'required|string',
                'price' => 'required|numeric|min:0',
                'image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
            ]);

            // Handle image upload
            if ($request->hasFile('image')) {
                // Delete old image
                if ($service->image) {
                    ImageHelper::deleteImage($service->image);
                }
                $data['image'] = ImageHelper::uploadImage($request->file('image'), 'services');
            }

            $service->update($data);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'อัปเดตบริการสำเร็จ',
                    'service' => $service,
                    'image_url' => $service->image_url
                ]);
            }

            return redirect()->route('admin.services.index')->with('success', 'อัปเดตบริการสำเร็จ');
        } catch (\Exception $e) {
            \Log::error('Error updating service: ' . $e->getMessage());

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'เกิดข้อผิดพลาดในการอัปเดตบริการ: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'เกิดข้อผิดพลาดในการอัปเดตบริการ');
        }
    }

    // หลังบ้าน: ลบบริการหลายรายการ
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:services,id'
        ]);

        try {
            $services = Service::whereIn('id', $request->ids)->get();

            // Delete images first
            foreach ($services as $service) {
                if ($service->image) {
                    ImageHelper::deleteImage($service->image);
                }
            }

            // Delete services
            Service::whereIn('id', $request->ids)->delete();

            return response()->json([
                'success' => true,
                'message' => 'ลบบริการ ' . count($request->ids) . ' รายการสำเร็จ'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการลบข้อมูล'
            ], 500);
        }
    }
}